import 'dart:async';
import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/login/bloc/login_bloc.dart';
import 'package:flutter_app_kouyu/widgets/weview_widget.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:formz/formz.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../common/style_util.dart';

class LoginForm extends StatefulWidget {
  const LoginForm({super.key});

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final TextEditingController _phoneTextField = TextEditingController();
  final TextEditingController _codeTextField = TextEditingController();

  static const String _agreedToAgreementKey = 'agreed_to_user_agreement';
  bool _hasShownAgreementDialog = false;
  bool _isAgreementChecked = false; // 修改默认为未勾选

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAgreementStatus();
    });
  }

  @override
  void dispose() {
    _phoneTextField.dispose();
    _codeTextField.dispose();
    super.dispose();
  }

  Future<void> _checkAgreementStatus() async {
    if (_hasShownAgreementDialog) {
      return;
    }

    final prefs = await SharedPreferences.getInstance();
    final hasAgreed = prefs.getBool(_agreedToAgreementKey) ?? false;

    if (!Platform.isIOS && !hasAgreed) {
      _showAgreementDialog();
      _hasShownAgreementDialog = true;
    }
  }

  /// 获取会员协议链接
  String getMembershipAgreementUrl() {
    if (Platform.isIOS) {
      return "https://cos.xinquai.com/agreements_and_notes/ios_Membership_Agreement.html";
    } else {
      return "https://cos.xinquai.com/agreements_and_notes/android_Membership_Agreement.html";
    }
  }

  ///获取隐私声明链接
  String getPrivacyDeclarationUrl() {
    if (Platform.isIOS) {
      return "https://cos.xinquai.com/agreements_and_notes/ios_privacy_agreement.html";
    } else {
      return "https://cos.xinquai.com/agreements_and_notes/android_privacy_agreement.html";
    }
  }

  RichText _protocolWidget(BuildContext context) {
    return RichText(
        textAlign: TextAlign.left,
        text: TextSpan(text: "", children: [
          WidgetSpan(
              alignment: PlaceholderAlignment.middle,
              child: Checkbox(
                value: _isAgreementChecked,
                shape: const CircleBorder(),
                activeColor: const Color(0xFF2693FF), // 设置勾选框颜色与登录按钮一致
                onChanged: (value) {
                  setState(() {
                    _isAgreementChecked = value ?? false;
                  });
                },
              )),
          const TextSpan(
            text: "点击登录或完成账号注册即代表您已阅读并同意\n",
            style: TextStyle(
                color: Color(0xFFA3C5CC),
                fontSize: 12,
                fontWeight: FontWeight.w400),
          ),
          WidgetSpan(
            alignment: PlaceholderAlignment.middle,
            child: SizedBox(width: 24), // 添加与勾选框宽度相同的空白，使第二行文本与第一行的"点"字对齐
          ),
          TextSpan(
              style: const TextStyle(
                  color: Color(0xFF41C0FF),
                  fontSize: 12,
                  fontWeight: FontWeight.w400),
              text: '《用户服务协议》',
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Navigator.push(context, MaterialPageRoute(builder: (context) {
                    return WebviewWidget(
                      url: Platform.isIOS
                          ? "https://cos.xinquai.com/agreements_and_notes/ios_Membership_Agreement.html"
                          : "https://cos.xinquai.com/agreements_and_notes/android_Membership_Agreement.html",
                    );
                  }));
                }),
          const TextSpan(
              style: TextStyle(
                  color: Color(0xFFA3C5CC),
                  fontSize: 12,
                  fontWeight: FontWeight.w400),
              text: "及"),
          TextSpan(
              style: const TextStyle(
                  color: Color(0xFF41C0FF),
                  fontSize: 12,
                  fontWeight: FontWeight.w400),
              text: "《隐私声明》",
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Navigator.push(context, MaterialPageRoute(builder: (context) {
                    return WebviewWidget(
                      url: Platform.isIOS
                          ? "https://cos.xinquai.com/agreements_and_notes/ios_privacy_agreement.html"
                          : "https://cos.xinquai.com/agreements_and_notes/android_privacy_agreement.html",
                    );
                  }));
                }),
        ]));
  }

  void _showAgreementDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
            backgroundColor: Colors.white,
            contentPadding: EdgeInsets.all(30.w),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.0),
            ),
            title: Text(
              "请阅读用户协议",
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 35.sp, fontWeight: FontWeight.bold),
            ),
            content: Container(
              height: 500.w,
              width: 300.w,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 10.w),
                    Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                            text: "欢迎使用小西口语！在您使用前，请认真阅读并了解",
                            style: TextStyle(fontSize: 25.sp),
                          ),
                          TextSpan(
                            text: "《用户服务协议》",
                            style: TextStyle(
                              fontSize: 25.sp,
                              color: Colors.blue,
                              decoration: TextDecoration.underline,
                            ),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                launchUrl(Uri.parse(Platform.isIOS
                                    ? 'https://cos.xinquai.com/agreements_and_notes/ios_Membership_Agreement.html'
                                    : 'https://cos.xinquai.com/agreements_and_notes/android_Membership_Agreement.html'));
                              },
                          ),
                          TextSpan(
                            text: "及",
                            style: TextStyle(fontSize: 25.sp),
                          ),
                          TextSpan(
                            text: "《隐私声明》",
                            style: TextStyle(
                              fontSize: 25.sp,
                              color: Colors.blue,
                              decoration: TextDecoration.underline,
                            ),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                launchUrl(Uri.parse(Platform.isIOS
                                    ? 'https://cos.xinquai.com/agreements_and_notes/ios_privacy_agreement.html'
                                    : 'https://cos.xinquai.com/agreements_and_notes/android_privacy_agreement.html'));
                              },
                          ),
                          TextSpan(
                            text: "：",
                            style: TextStyle(fontSize: 25.sp),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 10.w),
                    Text("一、权限使用说明",
                        style: TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 25.sp)),
                    Text("1、为保障文件下载与存储功能，我们可能申请存储权限；",
                        style: TextStyle(fontSize: 25.sp)),
                    Text("2、为支持聊天、配音、跟读及问题反馈，我们可能申请麦克风权限；",
                        style: TextStyle(fontSize: 25.sp)),
                    Text("3、为精准定位问题原因，我们可能收集设备信息、浏览器类型及日志信息；",
                        style: TextStyle(fontSize: 25.sp)),
                    Text("4、为头像更换、相册添加照片及可视化问题反馈，我们可能申请相册与摄像头权限；",
                        style: TextStyle(fontSize: 25.sp)),
                    Text("5、为个性化学习内容推荐，我们可能申请定位/位置权限；",
                        style: TextStyle(fontSize: 25.sp)),
                    Text("6、为身份验证与交易风控，我们可能获取手机IMEI及设备MAC地址；",
                        style: TextStyle(fontSize: 25.sp)),
                    Text("7、为实现内容分享至第三方应用，我们可能申请安装列表权限。",
                        style: TextStyle(fontSize: 25.sp)),
                    SizedBox(height: 10.w),
                    Text("二、第三方SDK说明",
                        style: TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 25.sp)),
                    Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                            text: "产品中接入了第三方SDK，您可通过",
                            style: TextStyle(fontSize: 25.sp),
                          ),
                          TextSpan(
                            text: "《隐私声明》",
                            style: TextStyle(
                              fontSize: 25.sp,
                              color: Colors.blue,
                              decoration: TextDecoration.underline,
                            ),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                launchUrl(
                                    Uri.parse(getPrivacyDeclarationUrl()));
                              },
                          ),
                          TextSpan(
                            text: "了解其个人信息收集情况。",
                            style: TextStyle(fontSize: 25.sp),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 10.w),
                    Text("三、您的权利保障",
                        style: TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 25.sp)),
                    Text("您可随时访问、更正或删除个人信息，并享有账户注销与投诉渠道。",
                        style: TextStyle(fontSize: 25.sp)),
                    SizedBox(height: 10.w),
                    Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                            text: "请务必阅读完整",
                            style: TextStyle(fontSize: 25.sp),
                          ),
                          TextSpan(
                            text: "《用户服务协议》",
                            style: TextStyle(
                              fontSize: 25.sp,
                              color: Colors.blue,
                              decoration: TextDecoration.underline,
                            ),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                launchUrl(
                                    Uri.parse(getMembershipAgreementUrl()));
                              },
                          ),
                          TextSpan(
                            text: "以获取详细信息。",
                            style: TextStyle(fontSize: 25.sp),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              Row(
                children: [
                  Expanded(
                    child: Container(
                      height: 70.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.grey.withOpacity(0.2),
                      ),
                      child: TextButton(
                          onPressed: () {
                            SystemNavigator.pop();
                          },
                          style: ButtonStyle(
                            textStyle: MaterialStatePropertyAll(TextStyle(
                                fontSize: 25.sp, fontWeight: FontWeight.w700)),
                          ),
                          child: Center(
                            child: Text("拒绝", style: style_1_28),
                          )),
                    ),
                  ),
                  SizedBox(width: 20.w),
                  Expanded(
                    child: Container(
                      height: 70.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: const Color(0xFF2693FF),
                      ),
                      child: TextButton(
                          onPressed: () async {
                            final prefs = await SharedPreferences.getInstance();
                            await prefs.setBool(_agreedToAgreementKey, true);
                            Navigator.of(context).pop();
                          },
                          style: ButtonStyle(
                            textStyle: MaterialStatePropertyAll(TextStyle(
                                fontSize: 25.sp, fontWeight: FontWeight.w700)),
                          ),
                          child: Center(
                            child: Text(
                              "同意",
                              style: style_1_28.copyWith(color: Colors.white),
                            ),
                          )),
                    ),
                  ),
                ],
              )
            ]);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<LoginBloc, LoginState>(
      listener: (context, state) {
        // if (state.status.isInitial) {
        //   EasyLoading.dismiss();
        // }
        // if (state.status.isFailure) {
        //   EasyLoading.showError("登录失败");
        // }
        // if (state.status.isInProgress) {
        //   EasyLoading.show(status: "登录中");
        // }
        // if (state.status.isSuccess) {
        //   EasyLoading.showSuccess("登录成功");
        //   if (state.loginInfoModel?.data?.enterTheProcess == true) {
        //     Navigator.of(context).pushNamedAndRemoveUntil(
        //         "/setup_setting_one", (route) => false);
        //   } else {
        //     LoginUtil.checkAppVersion = true;
        //     Navigator.of(context)
        //         .pushNamedAndRemoveUntil("/home", (route) => false);
        //   }
        // }
      },
      child: Column(
        children: [
          Expanded(child: _body(context)),
          Container(
            padding:
                const EdgeInsets.symmetric(horizontal: 20), // 与登录按钮保持相同的水平边距
            alignment: Alignment.centerLeft, // 左对齐
            child: _protocolWidget(context),
          ),
          const SizedBox(
            height: 40,
          )
        ],
      ),
    );
  }

  // 添加未勾选协议时的提示弹框
  void _showAgreementReminderDialog() {
    // 保存外部 context
    final outerContext = context; // 在 showDialog 之前获取外部 context
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10.0),
          ),
          title: Text(
            "你是否已阅读并同意",
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          content: Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: "点击登录或完成账号注册即代表您已阅读并同意",
                  style: TextStyle(
                    color: Color(0xFFA3C5CC),
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                TextSpan(
                  text: '《用户服务协议》',
                  style: TextStyle(
                    color: Color(0xFF41C0FF),
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      Navigator.push(context,
                          MaterialPageRoute(builder: (context) {
                        return WebviewWidget(
                          url: Platform.isIOS
                              ? "https://cos.xinquai.com/agreements_and_notes/ios_Membership_Agreement.html"
                              : "https://cos.xinquai.com/agreements_and_notes/android_Membership_Agreement.html",
                        );
                      }));
                    },
                ),
                TextSpan(
                  text: "及",
                  style: TextStyle(
                    color: Color(0xFFA3C5CC),
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                TextSpan(
                  text: "《隐私声明》",
                  style: TextStyle(
                    color: Color(0xFF41C0FF),
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      Navigator.push(context,
                          MaterialPageRoute(builder: (context) {
                        return WebviewWidget(
                          url: Platform.isIOS
                              ? "https://cos.xinquai.com/agreements_and_notes/ios_privacy_agreement.html"
                              : "https://cos.xinquai.com/agreements_and_notes/android_privacy_agreement.html",
                        );
                      }));
                    },
                ),
              ],
            ),
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                          color: const Color(0xFF2693FF), width: 2.0), // 加粗边框
                    ),
                    child: TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: ButtonStyle(
                        textStyle: MaterialStatePropertyAll(TextStyle(
                            fontSize: 18, fontWeight: FontWeight.w700)),
                      ),
                      child: Text("不同意",
                          style: style_1_28.copyWith(
                              color: const Color(0xFF2693FF))), // 修改文字颜色
                    ),
                  ),
                ),
                SizedBox(width: 20),
                Expanded(
                  child: Container(
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: const Color(0xFF2693FF),
                    ),
                    child: TextButton(
                      onPressed: () {
                        // 使用 dialogContext 关闭对话框
                        Navigator.of(dialogContext).pop();
                        // 使用 outerContext 访问 LoginBloc
                        SharedPreferences.getInstance().then((prefs) {
                          prefs.setBool(_agreedToAgreementKey, true);
                        });
                        // Navigator.of(context).pop();

                        // 先执行勾选状态更新
                        setState(() {
                          _isAgreementChecked = true;
                        });

                        // 使用 outerContext 访问 LoginBloc
                        if (outerContext.read<LoginBloc>().state.isValid) {
                          outerContext
                              .read<LoginBloc>()
                              .add(const LoginSubmitted());
                        }
                      },
                      style: ButtonStyle(
                        textStyle: MaterialStatePropertyAll(TextStyle(
                            fontSize: 18, fontWeight: FontWeight.w700)),
                      ),
                      child: Text(
                        "同意并继续",
                        style: style_1_28.copyWith(color: Colors.white),
                      ),
                    ),
                  ),
                ),
              ],
            )
          ],
        );
      },
    );
  }

  Widget _body(BuildContext context) {
    return BlocBuilder<LoginBloc, LoginState>(builder: (context, state) {
      return Column(children: [
        GestureDetector(
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: SizedBox(
            height: 280,
            child: Center(
              child: Stack(
                alignment: Alignment.topCenter,
                children: [
                  Image.asset(
                    "images/login_bg.png",
                    fit: BoxFit.fill,
                    width: double.infinity,
                    height: double.infinity,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: Image.asset(
                          fit: BoxFit.fill,
                          "images/icon_logo.png",
                          width: 100,
                          height: 100,
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      const Text(
                        "小西口语",
                        style: TextStyle(
                            fontWeight: FontWeight.w700,
                            fontSize: 20,
                            color: Color(0xFF333333)),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
        ClipRRect(
            borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20), topRight: Radius.circular(20)),
            child: Container(
              color: Colors.white,
              width: double.infinity,
              padding: const EdgeInsets.all(30),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "未注册手机号验证后自动创建账号",
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        fontSize: Platform.isIOS ? 15 : 12,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF646E70)),
                  ),
                  const SizedBox(height: 12),
                  Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: const Color(0xFFF3FBFD),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Expanded(
                              child: TextField(
                                key: const Key('loginForm_phone_textField'),
                                controller: _phoneTextField,
                                onChanged: (phone) => context
                                    .read<LoginBloc>()
                                    .add(LoginPhoneChanged(phone)),
                                textAlign: TextAlign.left,
                                keyboardType: TextInputType.number,
                                style: TextStyle(
                                    fontWeight: FontWeight.w800,
                                    fontSize: Platform.isIOS ? 18 : 14,
                                    color: Color(0xFF061B1F)),
                                decoration: InputDecoration(
                                    icon: Text(
                                      "+86",
                                      style: TextStyle(
                                          fontWeight: FontWeight.w800,
                                          fontSize: Platform.isIOS ? 18 : 14,
                                          color: Color(0xFF061B1F)),
                                    ),
                                    hintText: "输入手机号",
                                    suffixIcon: Offstage(
                                      offstage: state.phone.value.isEmpty,
                                      child: IconButton(
                                        icon: Image.asset(
                                          'images/tf_icon_delete.png',
                                          width: 17,
                                          height: 17,
                                        ),
                                        onPressed: () {
                                          _phoneTextField.clear();
                                          context
                                              .read<LoginBloc>()
                                              .add(const LoginPhoneChanged(''));
                                        },
                                      ),
                                    ),
                                    border: InputBorder.none,
                                    enabledBorder: InputBorder.none,
                                    focusedBorder: InputBorder.none,
                                    hintStyle: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontSize: Platform.isIOS ? 18 : 14,
                                        color: Color(0xFF8BA2A6))),
                              ),
                            ),
                          ],
                        ),
                      )),
                  const SizedBox(height: 12),
                  Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: const Color(0xFFF3FBFD),
                      ),
                      child: Row(
                        children: [
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextField(
                              key: const Key('loginForm_code_textField'),
                              onChanged: (value) {
                                context
                                    .read<LoginBloc>()
                                    .add(SmsCodeChanged(value));
                                if (value.length >= 6) {
                                  FocusScope.of(context)
                                      .requestFocus(FocusNode());
                                }
                              },
                              textAlign: TextAlign.left,
                              keyboardType: TextInputType.number,
                              controller: _codeTextField,
                              style: TextStyle(
                                  fontWeight: FontWeight.w800,
                                  fontSize: Platform.isIOS ? 18 : 14,
                                  color: Color(0xFF061B1F)),
                              decoration: InputDecoration(
                                  hintText: "输入验证码",
                                  border: InputBorder.none,
                                  suffixIcon: Offstage(
                                    offstage: state.smsCode.value.isEmpty,
                                    child: IconButton(
                                      icon: Image.asset(
                                        'images/tf_icon_delete.png',
                                        width: 17,
                                        height: 17,
                                      ),
                                      onPressed: () {
                                        _codeTextField.text = '';
                                        context
                                            .read<LoginBloc>()
                                            .add(const SmsCodeChanged(''));
                                      },
                                    ),
                                  ),
                                  hintStyle: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontSize: Platform.isIOS ? 18 : 14,
                                      color: Color(0xFF8BA2A6))),
                            ),
                          ),
                          _SmsButton(
                            phone: state.phone.value,
                          ),
                          const SizedBox(width: 16),
                        ],
                      )),
                  const SizedBox(height: 20),
                  Container(
                    height: 52,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: state.isValid
                          ? const Color(0xFF2693FF)
                          : const Color(0xFF2693FF).withOpacity(0.4),
                    ),
                    child: TextButton(
                        style: const ButtonStyle(
                          textStyle: MaterialStatePropertyAll(TextStyle(
                              fontSize: 20, fontWeight: FontWeight.w700)),
                        ),
                        onPressed: () {
                          if (state.isValid) {
                            if (_isAgreementChecked) {
                              context
                                  .read<LoginBloc>()
                                  .add(const LoginSubmitted());
                            } else {
                              _showAgreementReminderDialog();
                            }
                          }
                        },
                        child: Center(
                          child: Text(
                            "立即登录",
                            style: state.isValid
                                ? const TextStyle(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.w800)
                                : const TextStyle(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.w800),
                          ),
                        )),
                  ),
                ],
              ),
            ))
      ]);
    });
  }
}

class _SmsButton extends StatefulWidget {
  final String? phone;
  const _SmsButton({required this.phone});
  @override
  _SmsButtonState createState() => _SmsButtonState();
}

class _SmsButtonState extends State<_SmsButton> {
  String _buttonText = '发送验证码';
  int _count = 0;
  bool _buttonEnabled = true;
  Timer? _timer;
  void _startCountdown() {
    setState(() {
      _buttonEnabled = false;
      _buttonText = '60s';
    });

    _count = 60;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _count--;
        if (_count > 0) {
          _buttonText = '$_count s';
        } else {
          _count = 0;
          _buttonText = '发送验证码';
          _buttonEnabled = true;
          timer.cancel();
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.phone != null && widget.phone!.length == 11 && _count == 0) {
      _buttonEnabled = true;
    } else {
      _buttonEnabled = false;
    }

    TextStyle textStyle = const TextStyle(
        color: Colors.grey, fontWeight: FontWeight.w400, fontSize: 14);
    if (_buttonEnabled) {
      textStyle = textStyle.copyWith(color: const Color(0xFF41C0FF));
    } else {
      if (_count > 0) {
        textStyle = textStyle.copyWith(color: const Color(0xFF061B1F));
      }
    }

    return GestureDetector(
      onTap: () {
        if (_buttonEnabled) {
          Api.smsSend({"phone": widget.phone}).then((value) {
            EasyLoading.showToast("验证码发送成功");
            _startCountdown();
          });
        }
      },
      child: Text(_buttonText, style: textStyle),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}
