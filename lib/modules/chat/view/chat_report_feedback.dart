import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ChatReportFeedback extends StatelessWidget {
  const ChatReportFeedback({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24.w),
          color: Colors.white,
        ),
        // width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "学习反馈",
              style: style_1_36,
            ),
            SizedBox(
              height: 32.w,
            ),
            Text(
              "这个场景对你是否有帮助？",
              style: style_1_28,
            ),
            SizedBox(
              height: 16.w,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _optionWidget("几乎没有", 1),
                SizedBox(
                  width: 16.w,
                ),
                _optionWidget("有一点", 1),
                SizedBox(
                  width: 16.w,
                ),
                _optionWidget("很有帮助", 1)
              ],
            ),
            SizedBox(
              height: 32.w,
            ),
            Text(
              "这个场景对你是否有帮助？",
              style: style_1_28,
            ),
            SizedBox(
              height: 16.w,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _optionWidget("几乎没有", 1),
                SizedBox(
                  width: 16.w,
                ),
                _optionWidget("有一点", 1),
                SizedBox(
                  width: 16.w,
                ),
                _optionWidget("很有帮助", 1)
              ],
            ),
            SizedBox(
              height: 16.w,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _optionWidget("几乎没有", 1),
                SizedBox(
                  width: 16.w,
                ),
                _optionWidget("有一点", 1),
                SizedBox(
                  width: 16.w,
                ),
                _optionWidget("很有帮助", 1)
              ],
            ),
            SizedBox(
              height: 16.w,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _optionWidget("几乎没有", 1),
                SizedBox(
                  width: 16.w,
                ),
                _optionWidget("有一点", 1),
                SizedBox(
                  width: 16.w,
                ),
                _optionWidget("很有帮助", 1)
              ],
            ),
            SizedBox(
              height: 16.w,
            ),
            ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(12)),
              child: Container(
                color: const Color(0xFFF0F8FA),
                padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                        height: 176.w,
                        child: TextField(
                            onChanged: (value) {
                              // feedback = value;
                            },
                            maxLength: 200,
                            maxLines: 10,
                            decoration: InputDecoration(
                                hintText: "请输入您的建议或问题，以便我们提供更好的服务",
                                border: InputBorder.none,
                                enabledBorder: InputBorder.none,
                                focusedBorder: InputBorder.none,
                                hintStyle: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 24.w,
                                    color: const Color(0xFF8BA2A6))))),
                    SizedBox(
                      height: 16.w,
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              height: 16.w,
            ),
            Container(
              height: 88.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF98ECEC),
                      Color(0xFF84E9FF),
                      Color(0xFF9BE1FF)
                    ]),
              ),
              child: TextButton(
                  onPressed: () {},
                  style: const ButtonStyle(
                    textStyle: MaterialStatePropertyAll(
                        TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
                  ),
                  child: Center(
                    child: Text("提交", style: style_1_28),
                  )),
            ),
          ],
        ),
      ),
    );
  }

  Widget _optionWidget(String text, int type) {
    bool isSelected = false;
    return Expanded(
      child: Container(
        padding: const EdgeInsets.fromLTRB(6, 4, 6, 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: const Color(0xFFF0F8FA),
          border: Border.all(
            color: isSelected ? ColorUtil.blue : Colors.transparent,
            width: 0.5,
          ),
        ),
        child: Center(
            child: Text(text, style: isSelected ? style_bue_24 : style_2_24)),
      ),
    );
  }
}
