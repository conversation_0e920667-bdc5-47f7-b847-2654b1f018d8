import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app_kouyu/common/in_app_purchase/cubit/in_app_purchase_cubit.dart';
import 'package:flutter_app_kouyu/common/navigator/app_navigator.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/log.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/chat/view/change_teacher.dart';
import 'package:flutter_app_kouyu/modules/chat/view/chat_page.dart';
import 'package:flutter_app_kouyu/modules/chat/view/free_talk_page.dart';
import 'package:flutter_app_kouyu/modules/chat/view/vocabulary_practice_page.dart';
import 'package:flutter_app_kouyu/modules/find/pages/find_page.dart';
import 'package:flutter_app_kouyu/modules/ielts/pages/ielt_prescription_page.dart';
import 'package:flutter_app_kouyu/modules/ielts/pages/ielts_list_page.dart';
import 'package:flutter_app_kouyu/modules/login/view/login_page.dart';
import 'package:flutter_app_kouyu/modules/my/pages/about_us_page.dart';
import 'package:flutter_app_kouyu/modules/my/pages/enlish_level_choose_page.dart';
import 'package:flutter_app_kouyu/modules/my/pages/feedback_page.dart';
import 'package:flutter_app_kouyu/modules/my/pages/my_page.dart';
import 'package:flutter_app_kouyu/modules/my/pages/setup_settings_level.dart';
import 'package:flutter_app_kouyu/modules/my/pages/setup_settings_person.dart';
import 'package:flutter_app_kouyu/modules/my/pages/setup_settings_purpose.dart';
import 'package:flutter_app_kouyu/modules/my/pages/setup_settings_stage.dart';
import 'package:flutter_app_kouyu/modules/my/pages/user_settings.dart';
import 'package:flutter_app_kouyu/modules/quick_study/pages/study_page.dart';
import 'package:flutter_app_kouyu/modules/scene_study/pages/scene_study_page.dart';
import 'package:flutter_app_kouyu/modules/vip/view/vip_open_page.dart';
import 'package:flutter_app_kouyu/widgets/overlay_manager.dart';
import 'package:flutter_app_kouyu/widgets/player_widget.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'modules/chat/view/sence_introduce_page.dart';

final GlobalKey<NavigatorState> globalNavigatorKey =
    GlobalKey<NavigatorState>();

final InAppPurchaseCubit inAppPurchaseCubit = InAppPurchaseCubit();
final GlobalKey<MyHomePageState> myHomePageKey = GlobalKey<MyHomePageState>();

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(750, 1100),
      child: BlocProvider(
        create: (context) => inAppPurchaseCubit,
        child: MaterialApp(
          title: 'Flutter Demo',
          navigatorKey: globalNavigatorKey,
          navigatorObservers: [AppNavigator()],
          theme: ThemeData(
            scaffoldBackgroundColor: bgColor,
            textSelectionTheme: const TextSelectionThemeData(
              selectionColor: Color(0x1F41C0FF), //选中文字背景颜色
              selectionHandleColor: Color(0xFF41C0FF),
              cursorColor: Colors.orange, //光标颜色
            ),
            appBarTheme: const AppBarTheme(
              scrolledUnderElevation: 0.0,
              backgroundColor: Colors.transparent,
              systemOverlayStyle: SystemUiOverlayStyle.dark, // 强制黑色图标
            ), // AppBar 背景透明
          ),
          onGenerateRoute: (settings) {
            final routeName = settings.name;
            Log.d("routeName:$routeName");
            switch (routeName) {
              case "/login":
                return LoginPage.route();
              case "/home":
                return MyHomePage.route();
              case "/my":
                return MyPage.route();
              case "/free_talk":
                return FreeTalkPage.route(
                    argument: settings.arguments as TalkArgument);
              case "/open_vip":
                return VipOpenPage.route();
              case "/about_us":
                return AboutUsPage.route();
              case "/feedback":
                return FeedbackPage.route();
              case "/user_settings":
                return UserSettingsPage.route();

              case "/change_teacher":
                return ChangeTeacherPage.route();
              case "/setup_setting_one":
                return SetupSettingStage.route();
              case "/setup_setting_two":
                return SetupSettingLevel.route();
              case "/setup_setting_three":
                return SetupSettingPurpose.route();
              case "/setup_setting_four":
                return SetupSettingPerson.route();
              case "/enlish_level_choose":
                return EnlishLevelChoosePage.route();
              case "/sence_introduce":
                return SenceIntroducePage.route(settings);
              case "/vocabulary_practice":
                return VocabularyPracticePage.route(settings);
              case "/examination_prescription":
                return IeltsPrescriptionPage.route();
              case "/quick_study":
                return StudyPage.route(type: settings.arguments as String);
              default:
                return null;
            }
          },
          builder: EasyLoading.init(
            builder: (context, child) {
              return MediaQuery(
                //设置全局的文字的textScaleFactor为1.0，文字不再随系统设置改变
                data: MediaQuery.of(context)
                    .copyWith(textScaler: TextScaler.noScaling),
                child: child!,
              );
            },
          ),
          home: FutureBuilder(
              future: LoginUtil.isLogin(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const CircularProgressIndicator(); //加载中
                } else if (snapshot.connectionState == ConnectionState.done) {
                  //加载完成
                  if (snapshot.hasError) {
                    return const Text('Error');
                  } else if (snapshot.hasData && snapshot.data == true) {
                    return MyHomePage(
                      key: myHomePageKey,
                    ); //已经登录过 直接进入首页
                  } else {
                    return const LoginPage(); //没有登录过 进入登录页面
                  }
                } else {
                  //异常
                  return Text('State: ${snapshot.connectionState}');
                }
              }),
        ),
      ),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  static Route<void> route() {
    return MaterialPageRoute<void>(
        builder: (_) => MyHomePage(
              key: myHomePageKey,
            ));
  }
  // This widget is the home page of your application. It is stateful, meaning
  // that it has a State object (defined below) that contains fields that affect
  // how it looks.

  // This class is the configuration for the state. It holds the values (in this
  // case the title) provided by the parent (in this case the App widget) and
  // used by the build method of the State. Fields in a Widget subclass are
  // always marked "final".

  @override
  State<MyHomePage> createState() => MyHomePageState();
}

class MyHomePageState extends State<MyHomePage> {
  //底部导航栏坐标
  int _index = 0;
  List<BottomNavigationBarModel> models = [
    // BottomNavigationBarModel(
    // page: const FindPage(), label: "发现", icon: const Icon(Icons.settings)),
    BottomNavigationBarModel(
        page: const ChatPage(),
        label: "首页",
        icon: Image.asset(
          "images/bar_chat.png",
          width: 24,
          height: 24,
        ),
        activeIcon: Image.asset(
          "images/bar_chat_selected.png",
          width: 24,
          height: 24,
        )),
    BottomNavigationBarModel(
        page: SceneStudyPage(tabIndex: 0),
        label: "课程",
        icon: Image.asset(
          "images/bar_study.png",
          width: 24,
          height: 24,
        ),
        activeIcon: Image.asset(
          "images/bar_study_selected.png",
          width: 24,
          height: 24,
        )),
    BottomNavigationBarModel(
        page: const IeltsListPage(),
        label: "雅思",
        icon: Image.asset(
          "images/bar_yasi.png",
          width: 24,
          height: 24,
        ),
        activeIcon: Image.asset(
          "images/bar_yasi_selected.png",
          width: 24,
          height: 24,
        )),
    BottomNavigationBarModel(
        page: const FindPage(),
        label: "探索",
        icon: Image.asset(
          "images/bar_tansuo.png",
          width: 24,
          height: 24,
        ),
        activeIcon: Image.asset(
          "images/bar_tansuo_selected.png",
          width: 24,
          height: 24,
        )),
    BottomNavigationBarModel(
        page: const MyPage(),
        label: "我的",
        icon: Image.asset(
          "images/bar_user.png",
          width: 24,
          height: 24,
        ),
        activeIcon: Image.asset(
          "images/bar_user_selected.png",
          width: 24,
          height: 24,
        )),
  ];

  changeIeltsPage() {
    setState(() {
      _index = 2;
    });
  }

  changeLeanToolsPage() {
    setState(() {
      _index = 3;
    });
  }

  changeSceneStudyPage() {
    setState(() {
      _index = 1;
    });
  }

  @override
  Widget build(BuildContext context) {
    // This method is rerun every time setState is called, for instance as done
    // by the _incrementCounter method above.
    //
    // The Flutter framework has been optimized to make rerunning build methods
    // fast, so that you can just rebuild anything that needs updating rather
    // than having to individually change instances of widgets.
    OverlayManager.initInstance(context);

    return PlayerContainer(
      child: Scaffold(
        // appBar: AppBar(
        //   // TRY THIS: Try changing the color here to a specific color (to
        //   // Colors.amber, perhaps?) and trigger a hot reload to see the AppBar
        //   // change color while the other colors stay the same.
        //   backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        //   // Here we take the value from the MyHomePage object that was created by
        //   // the App.build method, and use it to set our appbar title.
        //   title: Text(widget.title),
        // ),
        body: models[_index].page,
        bottomNavigationBar: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          items: List.generate(models.length, (index) {
            return BottomNavigationBarItem(
                icon: models[index].icon,
                label: models[index].label,
                activeIcon: models[index].activeIcon);
          }),
          currentIndex: _index,
          selectedLabelStyle: const TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w400,
              color: Color(0xFF2693FF)),
          unselectedLabelStyle: const TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w400,
              color: Color(0xFF748A99)),
          fixedColor: const Color(0xFF2693FF),
          onTap: (value) async {
            LogUtil.v('index = $value');
            if (value != 0) {
              //判断是否登录
              if (!(await LoginUtil.isLogin())) {
                Navigator.of(context).pushNamed("/login");
                return;
              }
            }
            setState(() {
              _index = value;
            });
          },
        ),
      ),
    );
  }
}

class BottomNavigationBarModel {
  final String label;
  final Widget icon;
  final Widget activeIcon;
  final Widget page;

  BottomNavigationBarModel(
      {required this.label,
      required this.icon,
      required this.page,
      required this.activeIcon});
}
